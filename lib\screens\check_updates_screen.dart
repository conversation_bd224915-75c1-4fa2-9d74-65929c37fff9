import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../services/app_update_service.dart';

class CheckUpdatesScreen extends StatefulWidget {
  const CheckUpdatesScreen({super.key});

  @override
  State<CheckUpdatesScreen> createState() => _CheckUpdatesScreenState();
}

class _CheckUpdatesScreenState extends State<CheckUpdatesScreen> {
  bool _isChecking = false;
  String _currentVersion = '';
  String _updateStatus = '';
  bool _hasUpdate = false;

  @override
  void initState() {
    super.initState();
    _getCurrentVersion();
  }

  Future<void> _getCurrentVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _currentVersion = packageInfo.version;
      });
    } catch (e) {
      debugPrint('خطأ في جلب معلومات التطبيق: $e');
    }
  }

  Future<void> _checkForUpdates() async {
    if (_isChecking) return;

    setState(() {
      _isChecking = true;
      _updateStatus = 'جاري التحقق من التحديثات...';
      _hasUpdate = false;
    });

    try {
      // استخدام خدمة التحديث للتحقق من وجود تحديثات جديدة
      await AppUpdateService().checkForUpdate(context);
      
      // بعد الانتهاء من التحقق، نعرض رسالة مناسبة
      // ملاحظة: هذا لا يعكس بالضرورة ما إذا كان هناك تحديث أم لا
      // لأن خدمة التحديث تعرض الحوار بنفسها إذا وجدت تحديثًا
      setState(() {
        _isChecking = false;
        _updateStatus = 'تم التحقق من التحديثات';
      });
    } catch (e) {
      setState(() {
        _isChecking = false;
        _updateStatus = 'حدث خطأ أثناء التحقق من التحديثات';
      });
      debugPrint('خطأ في التحقق من التحديثات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'التحقق من التحديثات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).scaffoldBackgroundColor,
              Theme.of(context).scaffoldBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة التحديث
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366f1), Color(0xFFec4899)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: _isChecking
                      ? const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        )
                      : const Icon(
                          Icons.system_update,
                          size: 60,
                          color: Colors.white,
                        ),
                ),
                const SizedBox(height: 40),
                
                // معلومات الإصدار الحالي
                Text(
                  'الإصدار الحالي',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _currentVersion,
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF6366f1),
                  ),
                ),
                const SizedBox(height: 20),
                
                // حالة التحديث
                Text(
                  _updateStatus,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: _hasUpdate ? Colors.green : Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // زر التحقق من التحديثات
                ElevatedButton(
                  onPressed: _isChecking ? null : _checkForUpdates,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366f1),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 15,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    minimumSize: const Size(250, 50),
                  ),
                  child: Text(
                    _isChecking ? 'جاري التحقق...' : 'التحقق من التحديثات',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}